'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import Link from 'next/link';
import {
  ArrowLeft,
  ShoppingCart,
  Filter,
  User,
  Mail,
  Phone,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { 
  getContactMessages, 
  updateContactMessageStatus,
  subscribeToContactMessages 
} from '@/lib/firebaseServices';
import { ContactMessage } from '@/types';
import StatusDropdown from '@/components/admin/StatusDropdown';

export default function PurchaseRequestsPage() {
  const { user, userData } = useAuth();
  const [purchaseRequests, setPurchaseRequests] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<ContactMessage | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const messages = await getContactMessages();
        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));
      } catch (error: any) {
        console.error('Error fetching purchase requests:', error);
        setError('Failed to load purchase requests');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listener
      const unsubscribe = subscribeToContactMessages((messages) => {
        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));
      });

      return () => unsubscribe();
    }
  }, [user, userData]);

  const handleUpdateStatus = async (requestId: string, status: ContactMessage['status']) => {
    try {
      await updateContactMessageStatus(requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: any) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    }
  };

  const filteredRequests = purchaseRequests.filter(request => 
    statusFilter === 'all' || request.status === statusFilter
  );

  const getStatusCount = (status: string) => {
    return purchaseRequests.filter(req => req.status === status).length;
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/admin">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Purchase Requests
          </h1>
          <p className="text-gray-600">
            Manage template purchase requests from customers
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-xl font-bold">{purchaseRequests.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-yellow-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-xl font-bold">{getStatusCount('pending')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Confirmed</p>
                    <p className="text-xl font-bold">{getStatusCount('confirmed')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Approved</p>
                    <p className="text-xl font-bold">{getStatusCount('approved')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-gray-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-xl font-bold">{getStatusCount('completed')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filter Section */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Purchase Requests</CardTitle>
                <CardDescription>
                  Manage template purchase requests from customers
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="approved">Order Approved</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="declined">Declined</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading purchase requests...</p>
                </div>
              </div>
            )}

            {/* Requests List */}
            {!loading && (
              <div className="space-y-4">
                {filteredRequests.length > 0 ? (
                  filteredRequests.map((request) => (
                    <div 
                      key={request.id} 
                      className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => setSelectedRequest(request)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">{request.userName}</h4>
                            <Badge variant="default" className="text-xs">
                              Purchase Request
                            </Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {request.userEmail}
                            </p>
                            {request.userPhone && (
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {request.userPhone}
                              </p>
                            )}
                          </div>
                          <p className="text-sm font-medium text-gray-800 mt-1">{request.subject}</p>
                          <p className="text-sm text-gray-500 mt-2 line-clamp-2">{request.message}</p>
                          {request.templateTitle && (
                            <p className="text-xs text-blue-600 mt-1">Template: {request.templateTitle}</p>
                          )}
                          <p className="text-xs text-gray-400 mt-2 flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                          <StatusDropdown
                            currentStatus={request.status}
                            onStatusChange={(status) => handleUpdateStatus(request.id, status)}
                            type="purchase-request"
                          />
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchase requests</h3>
                    <p className="text-gray-600">
                      {statusFilter === 'all' 
                        ? 'Purchase requests from customers will appear here.'
                        : `No ${statusFilter} purchase requests found.`
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Detailed Request View Dialog */}
        {selectedRequest && (
          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Purchase Request Details</DialogTitle>
                <DialogDescription>
                  Complete information about this purchase request
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                {/* User Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Customer Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Name</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userEmail}</p>
                    </div>
                    {selectedRequest.userPhone && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-sm text-gray-900">{selectedRequest.userPhone}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <div className="mt-1">
                        <StatusDropdown
                          currentStatus={selectedRequest.status}
                          onStatusChange={(status) => handleUpdateStatus(selectedRequest.id, status)}
                          type="purchase-request"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Request Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Request Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subject</label>
                      <p className="text-sm text-gray-900">{selectedRequest.subject}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Message</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedRequest.message}</p>
                    </div>
                    {selectedRequest.templateTitle && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Template</label>
                        <p className="text-sm text-blue-600">{selectedRequest.templateTitle}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timeline */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created At</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.createdAt.seconds ? selectedRequest.createdAt.seconds * 1000 : selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Updated</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.updatedAt?.seconds ? selectedRequest.updatedAt.seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
