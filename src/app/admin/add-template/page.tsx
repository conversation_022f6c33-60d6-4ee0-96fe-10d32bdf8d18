'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Plus, Upload, Star } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { addTemplate } from '@/lib/firebaseServices';
import { toast } from 'sonner';

export default function AddTemplate() {
  const { user, userData } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    customCategory: '',
    price: '',
    minPrice: '',
    maxPrice: '',
    features: '',
    demoUrl: '',
    downloadUrl: '',
    imageUrl: '',
    tags: '',
    featured: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare template data
      const finalCategory = formData.category === 'Other' ? formData.customCategory.trim() : formData.category;

      const templateData: any = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: finalCategory,
        price: parseFloat(formData.price) || 0,
        imageUrl: formData.imageUrl.trim(),
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        featured: formData.featured,
        premium: parseFloat(formData.price) > 0,
        free: parseFloat(formData.price) === 0,
        rating: 4.5,
        downloads: 0,
        createdBy: user?.uid || 'admin'
      };

      // Only add optional fields if they have values
      if (formData.minPrice && formData.minPrice.trim()) {
        templateData.minPrice = parseFloat(formData.minPrice);
      }
      if (formData.maxPrice && formData.maxPrice.trim()) {
        templateData.maxPrice = parseFloat(formData.maxPrice);
      }
      if (formData.demoUrl && formData.demoUrl.trim()) {
        templateData.demoUrl = formData.demoUrl.trim();
      }
      if (formData.downloadUrl && formData.downloadUrl.trim()) {
        templateData.downloadUrl = formData.downloadUrl.trim();
      }
      if (formData.features && formData.features.trim()) {
        templateData.keyFeatures = formData.features.trim();
      }

      // Add template to Firebase
      await addTemplate(templateData);

      toast.success('Template added successfully!');

      // Reset form
      setFormData({
        title: '',
        description: '',
        category: '',
        price: '',
        minPrice: '',
        maxPrice: '',
        features: '',
        demoUrl: '',
        downloadUrl: '',
        imageUrl: '',
        tags: '',
        customCategory: '',
        featured: false
      });

      // Redirect to admin page after a short delay
      setTimeout(() => {
        router.push('/admin');
      }, 1500);

    } catch (error) {
      console.error('Error adding template:', error);
      toast.error('Failed to add template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'featured' ? (typeof value === 'boolean' ? value : value === 'true') : value
    }));
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/admin">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Admin
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Add New Template
          </h1>
          <p className="text-gray-600">
            Add a new template to your marketplace
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
              <CardTitle className="flex items-center text-2xl">
                <Plus className="mr-3 h-6 w-6 text-blue-600" />
                Template Details
              </CardTitle>
              <CardDescription className="text-base">
                Fill in the information for your new template. All fields marked with * are required.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium">Template Title *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="e.g., SaaS Dashboard Pro"
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category" className="text-sm font-medium">Category *</Label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Dashboard">Dashboard</SelectItem>
                          <SelectItem value="E-commerce">E-commerce</SelectItem>
                          <SelectItem value="Landing Page">Landing Page</SelectItem>
                          <SelectItem value="Portfolio">Portfolio</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Blog">Blog</SelectItem>
                          <SelectItem value="Business">Business</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Other">Other (Custom)</SelectItem>
                        </SelectContent>
                      </Select>
                      {formData.category === 'Other' && (
                        <Input
                          placeholder="Enter custom category"
                          value={formData.customCategory}
                          onChange={(e) => handleInputChange('customCategory', e.target.value)}
                          className="h-11 mt-2"
                          required
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Description Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Description & Details</h3>
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Provide a detailed description of your template, its features, and what makes it unique..."
                      rows={4}
                      className="resize-none"
                      required
                    />
                  </div>
                </div>

                {/* Media & Links Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Media & Links</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="imageUrl" className="text-sm font-medium">Image URL *</Label>
                      <Input
                        id="imageUrl"
                        type="url"
                        value={formData.imageUrl}
                        onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                        placeholder="https://images.unsplash.com/photo-..."
                        className="h-11"
                        required
                      />
                      <p className="text-xs text-gray-500">Use high-quality images from Unsplash or similar services</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="demoUrl" className="text-sm font-medium">Demo URL</Label>
                      <Input
                        id="demoUrl"
                        type="url"
                        value={formData.demoUrl}
                        onChange={(e) => handleInputChange('demoUrl', e.target.value)}
                        placeholder="https://demo.example.com"
                        className="h-11"
                      />
                    </div>
                  </div>
                </div>

                {/* Pricing & Features Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Pricing & Features</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="price" className="text-sm font-medium">Fixed Price (₹) *</Label>
                      <Input
                        id="price"
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', e.target.value)}
                        placeholder="2499"
                        min="0"
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="minPrice" className="text-sm font-medium">Min Price (₹)</Label>
                      <Input
                        id="minPrice"
                        type="number"
                        value={formData.minPrice}
                        onChange={(e) => handleInputChange('minPrice', e.target.value)}
                        placeholder="1999"
                        min="0"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Optional: For price ranges</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxPrice" className="text-sm font-medium">Max Price (₹)</Label>
                      <Input
                        id="maxPrice"
                        type="number"
                        value={formData.maxPrice}
                        onChange={(e) => handleInputChange('maxPrice', e.target.value)}
                        placeholder="4999"
                        min="0"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Optional: For price ranges</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="tags" className="text-sm font-medium">Tags</Label>
                      <Input
                        id="tags"
                        value={formData.tags}
                        onChange={(e) => handleInputChange('tags', e.target.value)}
                        placeholder="React, TypeScript, Tailwind CSS"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Separate tags with commas</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Template Settings</Label>
                      <div className="flex items-center space-x-3 p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50">
                        <Star className="h-5 w-5 text-yellow-500" />
                        <div className="flex-1">
                          <Label htmlFor="featured" className="text-sm font-medium text-gray-900">
                            Featured Template
                          </Label>
                          <p className="text-xs text-gray-600">Show this template on the home page</p>
                        </div>
                        <Switch
                          id="featured"
                          checked={formData.featured}
                          onCheckedChange={(checked) => handleInputChange('featured', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Features & Download Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Features & Download</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="features" className="text-sm font-medium">Features (one per line)</Label>
                      <Textarea
                        id="features"
                        value={formData.features}
                        onChange={(e) => handleInputChange('features', e.target.value)}
                        placeholder="Responsive Design&#10;Dark Mode Support&#10;Admin Dashboard&#10;User Authentication&#10;Modern UI Components"
                        rows={6}
                        className="resize-none"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="downloadUrl" className="text-sm font-medium">Download URL</Label>
                      <Input
                        id="downloadUrl"
                        type="url"
                        value={formData.downloadUrl}
                        onChange={(e) => handleInputChange('downloadUrl', e.target.value)}
                        placeholder="https://files.example.com/template.zip"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Direct link to downloadable template files</p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-8 border-t">
                  <Button type="button" variant="outline" size="lg" asChild>
                    <Link href="/admin">
                      Cancel
                    </Link>
                  </Button>
                  <Button type="submit" disabled={loading} size="lg" className="px-8">
                    {loading ? (
                      <>
                        <Upload className="mr-2 h-4 w-4 animate-spin" />
                        Adding Template...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Template
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
