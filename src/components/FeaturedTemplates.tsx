'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Eye, Download, ArrowRight, ShoppingCart, MessageCircle, Heart, ExternalLink, Zap, IndianRupee } from 'lucide-react';
import { Template } from '@/types';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { createContactMessage, toggleFavorite } from '@/lib/firebaseServices';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

// Fallback data if Firebase fails
const fallbackTemplates = [
  {
    id: '1',
    title: 'Modern Dashboard',
    description: 'Clean and modern dashboard template with dark mode support',
    category: 'Dashboard',
    price: 49,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['React', 'TypeScript', 'Tailwind']
  },
  {
    id: '2',
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and checkout',
    category: 'E-commerce',
    price: 79,
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center',
    rating: 4.8,
    downloads: 856,
    featured: true,
    tags: ['Next.js', 'Stripe', 'Responsive']
  },
  {
    id: '3',
    title: 'Landing Page Pro',
    description: 'High-converting landing page template for SaaS products',
    category: 'Landing Page',
    price: 39,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center',
    rating: 4.9,
    downloads: 2341,
    featured: true,
    tags: ['HTML', 'CSS', 'JavaScript']
  },
  {
    id: '4',
    title: 'Portfolio Showcase',
    description: 'Creative portfolio template for designers and developers',
    category: 'Portfolio',
    price: 29,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center',
    rating: 4.7,
    downloads: 1567,
    featured: true,
    tags: ['Vue.js', 'GSAP', 'Responsive']
  }
];

export const FeaturedTemplates = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, userData } = useAuth();
  const router = useRouter();

  useEffect(() => {
    const fetchFeaturedTemplates = async () => {
      try {
        // First try to get featured templates without ordering to avoid index requirement
        const q = query(
          collection(db, 'templates'),
          where('featured', '==', true),
          limit(8)
        );
        const querySnapshot = await getDocs(q);
        let fetchedTemplates = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Template[];

        // Sort by downloads in memory to avoid index requirement
        fetchedTemplates = fetchedTemplates
          .sort((a, b) => (b.downloads || 0) - (a.downloads || 0))
          .slice(0, 4);

        setTemplates(fetchedTemplates.length > 0 ? fetchedTemplates : fallbackTemplates);
      } catch (error) {
        console.error('Error fetching featured templates:', error);
        setTemplates(fallbackTemplates);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedTemplates();
  }, []);

  const handleContactRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to contact us')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before contacting us')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Inquiry about ${template.title}`,
        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information about customization options and pricing?`,
        type: 'contact',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Contact request sent! We\'ll get back to you soon.')
    } catch (error) {
      console.error('Error sending contact request:', error)
      toast.error('Failed to send contact request. Please try again.')
    }
  }

  const handleBuyRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to make a purchase request')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before making a purchase request')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Purchase Request for ${template.title}`,
        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment instructions and delivery details.`,
        type: 'purchase-request',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Purchase request sent! We\'ll contact you with payment details.')
    } catch (error) {
      console.error('Error sending buy request:', error)
      toast.error('Failed to send purchase request. Please try again.')
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent, template: Template) => {
    e.stopPropagation();

    if (!user) {
      toast.error('Please sign in to add favorites')
      return
    }

    try {
      const isAdded = await toggleFavorite(user.uid, template.id);
      if (isAdded) {
        toast.success('Added to favorites!')
      } else {
        toast.success('Removed from favorites!')
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('Failed to update favorites. Please try again.')
    }
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Featured Templates
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Hand-picked premium templates for your next project
          </p>
        </div>

        {/* Templates Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {templates.map((template) => (
              <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg cursor-pointer">
                {/* Image Container with Overlay */}
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                  {template.imageUrl ? (
                    <Image
                      src={template.imageUrl}
                      alt={template.title}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                      unoptimized
                    />
                  ) : null}
                  {!template.imageUrl && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <div className="w-12 sm:w-16 h-12 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                          <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                        </div>
                        <p className="text-muted-foreground font-medium text-sm sm:text-base">Template Preview</p>
                      </div>
                    </div>
                  )}

                  {/* Overlay with Quick Actions */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2 sm:gap-3">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white text-black border-0 text-xs sm:text-sm cursor-pointer"
                      onClick={() => window.open('/templates', '_blank')}
                    >
                      <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      Preview
                    </Button>
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-2 sm:top-3 left-2 sm:left-3">
                    <Badge variant="secondary" className="bg-white/90 text-black border-0 text-xs">
                      {template.category}
                    </Badge>
                  </div>

                  {/* Favorite Button */}
                  <div className="absolute top-2 sm:top-3 right-2 sm:right-3">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="w-6 h-6 sm:w-8 sm:h-8 p-0 bg-white/90 hover:bg-white text-black border-0 rounded-full cursor-pointer"
                      onClick={(e) => handleFavoriteClick(e, template)}
                    >
                      <Heart className={`h-3 w-3 sm:h-4 sm:w-4 ${userData?.favoriteTemplates?.includes(template.id) ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>
                  </div>
                </div>

                {/* Card Content */}
                <CardHeader className="pb-3">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-base sm:text-xl font-bold group-hover:text-blue-600 transition-colors line-clamp-2">
                        {template.title}
                      </CardTitle>
                      <div className="flex items-center gap-1 text-yellow-500 flex-shrink-0 ml-2">
                        <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-current" />
                        <span className="text-xs sm:text-sm font-medium">{template.rating || '4.9'}</span>
                      </div>
                    </div>
                    <p className="text-xs sm:text-sm leading-relaxed line-clamp-2 text-gray-600">
                      {template.description}
                    </p>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Features */}
                  <div className="flex flex-wrap gap-1 mb-3 sm:mb-4">
                    <Badge variant="outline" className="text-xs">Responsive</Badge>
                    <Badge variant="outline" className="text-xs">Modern</Badge>
                    <Badge variant="outline" className="text-xs">Fast</Badge>
                  </div>

                  {/* Price and Actions */}
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        {template.minPrice && template.maxPrice ? (
                          <span className="text-2xl sm:text-3xl font-bold text-green-600">
                            ₹{template.minPrice} - ₹{template.maxPrice}
                          </span>
                        ) : (
                          <span className="text-2xl sm:text-3xl font-bold text-green-600">₹{template.price}</span>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-muted-foreground line-through">₹{Math.round(template.price * 1.5)}</div>
                        <div className="text-xs text-green-600 font-medium">33% OFF</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          className="w-full group/btn hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all text-xs sm:text-sm cursor-pointer"
                          onClick={() => window.open('/customize', '_blank')}
                        >
                          <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                          <span className="hidden sm:inline">Preview</span>
                          <span className="sm:hidden">View</span>
                        </Button>

                        <Button
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn text-xs sm:text-sm cursor-pointer"
                          onClick={() => handleBuyRequest(template)}
                        >
                          <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                          <span className="hidden sm:inline">Contact to Buy</span>
                          <span className="sm:hidden">Buy</span>
                        </Button>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full group/btn hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-all text-xs sm:text-sm cursor-pointer"
                        onClick={() => handleContactRequest(template)}
                      >
                        <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                        <span className="hidden sm:inline">Contact for Info</span>
                        <span className="sm:hidden">Contact</span>
                      </Button>
                    </div>


                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* View All Button */}
        <div className="text-center">
          <Button asChild size="lg" variant="outline">
            <Link href="/templates">
              View All Templates
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};
